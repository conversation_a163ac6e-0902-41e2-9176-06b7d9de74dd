<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import ReportTableInfo from './reportTableInfo'
import { AdjustRecordColumns, CalcProcessColumns } from './defines/columns'
import { ReportRulerItem, RulerDetailItem } from '_/types/excessReserveManage/reportCheckRuler'
import { toThousands, getLabelFromEnum } from '_/utils'
import { adjustApi, cashFlowBoardApi, getAdjustRecords, getCalcProcess, getCheckFailRules, reportDetailApi, getCashPositionFlowBoard } from '_/api/excessReserve/index'
import { AdjustRecord, CalcProcess } from '_/types/excessReserveManage/reportDetails'
import { bankReportEnums } from '_/api'
export default defineComponent({
  name: 'ReportDetail',
  data() {
    return {
      AdjustRecordColumns,
      CalcProcessColumns,
      toThousands,
      getLabelFromEnum,
      /** 当前任务id */
      taskId: this.$route.params.taskId,
      organEnum: [] as Record<string, string>[],
      reportFileTypeEnum: [] as Record<string, string>[],
      /** 是否加载数据 */
      loadingData: false,
      loadingErrRules: false,
      loadingBoard: false,
      loadingBoardUnionPay: false,
      loadingCalcProcess: false,
      loadingAdjustRecords: false,
      /** 当前选中的公式 */
      currentTag: -1,
      /** 数据看板 */
      dataBoard: {
        cashNetFlow: 0,
        reportData: 0,
        isEqual: false,
      },
      dataBoardUnionPay: {
        accountsDate: '',
        d06: 0,
        cashAmount: 0,
        fluctuantFlag: 0,
      },
      showUnionPayBoard: false, // 是否显示银联看板
      /** 错误规则集合 */
      errRules: [] as ReportRulerItem[],
      currentErrRule: {} as ReportRulerItem,
      // 表信息：需要包含表名、列头columns、数据源、数据源字段数组
      reportTableInfo: ReportTableInfo,
      // 表数据源
      tableData: {
        REPORT_1_1: [
        ],
        REPORT_1_1_A: [
        ],
        REPORT_1_2: [
        ],
        REPORT_1_3: [
        ],
        REPORT_1_4: [
        ],
        REPORT_1_5: [
        ]
      } as Record<string, Array<Record<string, number>>>,
      // 当前清算机构
      currentClearOrg: '',
      // 当前任务记账日期
      currentDate: '',
      // 当前任务状态
      currentStatus: '',
      // 调整记录pop的显隐
      recordPopVisibles: {
      } as { [key:string]: boolean },
      curTable: '',
      curField: '',
      // 调账抽屉
      adjustDrawerVisible: false,
      adjustDrawerRules: {
        symbol: [
          { required: true, message: '请选择运算符号', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入调账金额', trigger: 'change' }
        ],
        remark: [
          { required: true, message: '请输入调账备注', trigger: 'change' }
        ]
      },
      // 计算过程
      calcProcess: [{
        __id: Math.random(),
        trxType: '数据调整',
        symbol: '+',
        payAmount: 123456789.12,
      }] as Array<CalcProcess>,
      // 调账记录
      adjustRecords: [
        // {
        //   __id: Math.random(),
        //   businessType: '数据调整',
        //   symbol: '+',
        //   amount: 123456789.12,
        //   calculated: 0,
        //   remark: '备注',
        // }
      ] as AdjustRecord[],
      // 调账form数据
      adjustForm: {
        bizType: '数据调整',
        reportTable: '',
        reportTableField: '',
        symbol: '+',
        amount: '',
        remark: '',
      },
      isSubmiting: false,
      adjustLayout: {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 }
      }
    }
  },
  computed: {
    /** 处理错误公式，以便控制元素样式 */
    errCells() {
      if (!this.currentErrRule) {
        return []
      }
      const L: RulerDetailItem[] = this.currentErrRule.detailVoL || []
      const R: RulerDetailItem[] = this.currentErrRule.detailVoR || []
      const result = L.concat(R).map((_: RulerDetailItem) => {
        return `${_.reportTable}_${_.reportField}`
      })
      return result
    },
  },
  watch: {
    adjustDrawerVisible(val) {
      if (!val) {
        this.adjustForm.amount = ''
        this.adjustForm.remark = ''
        this.adjustForm.symbol = '+'
        this.adjustForm.reportTable = ''
        this.adjustForm.reportTableField = ''
      }
    }
  },
  methods: {
    /**
     * @description 获取银联数据看板
     */
    getUnionPayData() {
      this.loadingBoardUnionPay = true
      getCashPositionFlowBoard({
        taskId: this.taskId
      }).then(res => {
        this.loadingBoardUnionPay = false
        if (res.code === 200) {
          this.dataBoardUnionPay = {
            accountsDate: res.data.accountsDate || '',
            d06: res.data.d06 || 0,
            cashAmount: res.data.cashAmount || 0,
            fluctuantFlag: res.data.fluctuantFlag || 0,
          }
        }
      }, () => {
        this.loadingBoardUnionPay = false
        this.dataBoardUnionPay = {
          accountsDate: '',
          d06: 0,
          cashAmount: 0,
          fluctuantFlag: 0,
        }
      }).finally(() => {
        this.loadingBoardUnionPay = false
      })
    },
    /**
     * @description 获取报表所有需要的数据
     */
    getReportData() {
      this.loadingData = true
      this.loadingErrRules = true
      this.loadingBoard = true
      // TODO 根据任务id获取报表详情
      reportDetailApi({
        taskId: this.taskId
      }).then(res => {
        if (res.code === 200) {
          // this.tableData = res.data.tableData
          Object.keys(this.tableData).forEach(key => {
            this.tableData[key] = res.data[key] ? [res.data[key]] : Array.from([{ id: Math.random() }])
          })
          // 设置清算机构currentClearOrg
          this.currentClearOrg = res.data.reportType || ''
          this.currentDate = res.data.accountsDate || ''
          this.currentStatus = res.data.status || ''
          // 是否显示银联看板
          if (this.currentClearOrg === 'UnionPay') {
            this.showUnionPayBoard = true
            this.getUnionPayData()
          }
        }
      }, () => {
        Object.keys(this.tableData).forEach(key => {
          this.tableData[key] = []
        })
        this.currentClearOrg = ''
        this.currentDate = ''
        this.currentStatus = ''
      }).finally(() => {
        this.loadingData = false
      })
      // TODO 获取错误公式
      getCheckFailRules({
        taskId: this.taskId
      }).then(res => {
        if (res.code === 200) {
          this.errRules = res.data.map((_: ReportRulerItem) => {
            return {
              ..._,
              expression: `${this.toExpression(_.detailVoL)}  ${_.checkMethod}  ${this.toExpression(_.detailVoR)}`
            }
          })
        }
      }, () => {
        this.errRules = []
      }).finally(() => {
        this.loadingErrRules = false
      })
      // TODO 获取数据看板
      cashFlowBoardApi({
        taskId: this.taskId
      }).then(res => {
        if (res.code === 200) {
          this.dataBoard.cashNetFlow = (res.data && res.data.cashFlow)
          this.dataBoard.reportData = (res.data && res.data.reportAmount)
          // 数据是否相等
          this.dataBoard.isEqual = (this.dataBoard.cashNetFlow === this.dataBoard.reportData) || false
        }
      }, () => {
        this.dataBoard.cashNetFlow = 0
        this.dataBoard.reportData = 0
        this.dataBoard.isEqual = false
      }).finally(() => {
        this.loadingBoard = false
      })
    },
    /**
     * @description 切换公式
     * @param tag 公式tag
     * @param checked 是否选中
     */
    handleChange(tag: ReportRulerItem, checked: boolean) {
      if (checked) {
        // 将现有的class带有check-red的元素去掉check-red
        this.currentTag = tag.id
        this.currentErrRule = tag
      } else {
        this.currentTag = -1
        this.currentErrRule = {} as ReportRulerItem
      }
    },
    toExpression(data: RulerDetailItem[]) {
      if (!Array.isArray(data)) {
        return
      }
      // data.reduce
      const result = data.reduce((prev, item, index) => {
        let _exp = ''
        // 处理符号
        if (index === 0) {
          // _exp+= item.symbol==='+'?'':'-'
          if (item.symbol === '+') _exp += ''
          else if (item.symbol === '-') _exp += '-'
          else _exp += '[unknow symbol]'
        } else {
          _exp += ` ${['+', '-'].includes(item.symbol) ? item.symbol : '[unknow symbol]'} `
        }
        // 处理表
        const _enum = this.reportFileTypeEnum.find((_item) => _item.value === item.reportTable)
        _exp += _enum ? _enum.label : '[unknow table]'
        // 连接符
        _exp += '.'
        // 处理字段
        _exp += item.reportField || '[unknow field]'
        return prev + _exp
      }, '')
      return result
    },
    /**
     * @description 获取调账记录数据
     */
    initAdjustRecord() {
      if (!this.recordPopVisibles[this.curTable + '_' + this.curField]) {
        return
      }
      // TODO 请求计算过程
      this.loadingCalcProcess = true
      this.loadingAdjustRecords = true
      getCalcProcess({
        reportType: this.currentClearOrg || '',
        accountsDate: this.currentDate || '',
        reportTable: this.curTable,
        reportField: this.curField,
      }).then(res => {
        if (res.code === 200) {
          this.calcProcess = res.data
        }
      }, () => {
        this.calcProcess = []
      }).finally(() => {
        this.loadingCalcProcess = false
      })
      // TODO 调整记录
      getAdjustRecords({
        taskId: this.taskId,
        reportTableField: this.curField,
        reportFileType: this.curTable,
      }).then(res => {
        if (res.code === 200) {
          this.adjustRecords = res.data
        }
      }, () => {
        this.adjustRecords = []
      }).finally(() => {
        this.loadingAdjustRecords = false
      })
    },
    /**
     * @description 关闭调账pop
     */
    closePop(curTable: string, curField: string) {
      this.$set(this.recordPopVisibles, curTable + '_' + curField, false)
    },
    /**
     * @description 获取调账记录数据
     * @param curTable 表名
     * @param curField 字段名
     */
    handleAdjustRecord(curTable: string, curField: string) {
      if (this.curTable === curTable && this.curField === curField) {
        this.$set(this.recordPopVisibles, this.curTable + '_' + this.curField, !this.recordPopVisibles[curTable + '_' + curField])
      } else {
        // 关闭上一个
        this.$set(this.recordPopVisibles, this.curTable + '_' + this.curField, false)
        this.curTable = curTable
        this.curField = curField
        // 打开这个
        this.$set(this.recordPopVisibles, curTable + '_' + curField, true)
      }
      this.initAdjustRecord()
    },
    /**
     * @description 打开调账抽屉
     * @param curTable 表名
     * @param curField 字段名
     */
    openAdjustModal(curTable: string, curField: string) {
      // 初始化数据
      this.adjustForm.reportTable = curTable
      this.adjustForm.reportTableField = curField
      this.recordPopVisibles[curTable + '_' + curField] = false
      this.adjustDrawerVisible = true
    },
    /**
     * @description 提交调账
     */
    submitAdjust() {
      // 验证
      this.$refs.adjustForm.validate((valid: boolean) => {
        if (valid) {
          this.isSubmiting = true
          // TODO 提交调账
          const data = {
            taskId: this.taskId,
            symbol: this.adjustForm.symbol as '+' | '-',
            amount: this.adjustForm.amount,
            bizType: this.adjustForm.bizType,
            reportFileType: this.adjustForm.reportTable,
            reportTableField: this.adjustForm.reportTableField,
            remark: this.adjustForm.remark,
          }
          adjustApi(data).then(res => {
            if (res.code === 200) {
              this.$message.success('调账成功')
              this.closeAdjustModal()
              this.getReportData()
            }
          }).finally(() => {
            this.isSubmiting = false
          })
          // this.closeAdjustModal()
        } else {
          return false
        }
      })
    },
    /**
     * @description 关闭调账抽屉
     */
    closeAdjustModal() {
      this.adjustDrawerVisible = false
    },
  },
  async mounted() {
    this.taskId = this.$route.params.taskId
    // 设置一下枚举
    bankReportEnums().then(res => {
      if (res.code === 200) {
        this.organEnum = res.data.organEnum.map((_: any) => {
          return {
            label: _.value,
            value: _.key
          }
        })
        this.reportFileTypeEnum = res.data.reportFileTypeEnum.map((_: any) => {
          return {
            label: _.value,
            value: _.key
          }
        })
      }
    })
    this.getReportData()
  },
  async activated() {
    this.taskId = this.$route.params.taskId
    this.getReportData()
  }
})
</script>
<template>
  <div class="p-5">
    <a-row class="summary-container" :gutter="[16, 16]">
      <a-col :span="7">
        <a-spin :spinning="loadingBoard">
          <a-card :title="`${ getLabelFromEnum(currentClearOrg, organEnum) || '--'}`">
            <span slot="extra">
              <!-- TODO -->
              <a-icon v-if="dataBoard.isEqual" type="check-circle" style="font-size: 1.25rem;color: #00af57;" theme="filled"/>
              <a-icon v-else type="close-circle" style="font-size: 1.25rem;color: #ff1e02;" theme="filled"/>
            </span>
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="记账日期">
                {{ currentDate || '--' }}
              </a-descriptions-item>
              <a-descriptions-item label="现金净流量(元)">
                {{ (dataBoard.cashNetFlow === 0 || dataBoard.cashNetFlow) ? toThousands(dataBoard.cashNetFlow) : '--' }}
              </a-descriptions-item>
              <a-descriptions-item label="报备数据(元)">
                {{ (dataBoard.reportData === 0 || dataBoard.reportData) ? toThousands(dataBoard.reportData) : '--' }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-spin>
      </a-col>
      <a-col v-if="showUnionPayBoard" :span="7">
        <a-spin :spinning="loadingBoardUnionPay">
          <a-card :title="`${ getLabelFromEnum(currentClearOrg, organEnum)+'-客户账' || '--'}`">
            <span slot="extra">
              <!-- TODO -->
              <a-icon v-if="dataBoard.isEqual" type="check-circle" style="font-size: 1.25rem;color: #00af57;" theme="filled"/>
              <a-icon v-else type="close-circle" style="font-size: 1.25rem;color: #ff1e02;" theme="filled"/>
            </span>
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="报备日期">
                {{ dataBoardUnionPay.accountsDate || '--' }}
              </a-descriptions-item>
              <a-descriptions-item label="客户账期末">
                {{ (dataBoardUnionPay.d06 === 0 || dataBoardUnionPay.d06) ? toThousands(dataBoardUnionPay.d06) : '--' }}
              </a-descriptions-item>
              <a-descriptions-item label="可用额度">
                {{ (dataBoardUnionPay.cashAmount === 0 || dataBoardUnionPay.cashAmount) ? toThousands(dataBoardUnionPay.cashAmount) : '--' }}
              </a-descriptions-item>
              <a-descriptions-item label="波 动">
                {{ (dataBoardUnionPay.fluctuantFlag === 0 || dataBoardUnionPay.fluctuantFlag) ? toThousands(dataBoardUnionPay.fluctuantFlag) : '--' }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-spin>
      </a-col>
      <a-col :span="10">
        <a-spin :spinning="loadingErrRules">
          <a-card title="核验失败公式">
            <div class="err-tag">
              <template v-for="tag in errRules">
                <a-checkable-tag
                  :key="tag.id"
                  :checked="tag.id === currentTag"
                  :pagination="false"
                  @change="checked => handleChange(tag, checked)"
                >
                  {{ tag.expression }}
                </a-checkable-tag>
              </template>
            </div>
          </a-card>
        </a-spin>
      </a-col>
    </a-row>
    <a-divider></a-divider>
    <a-spin :spinning="loadingData">
      <div class="report-containe">
        <!-- 表名、列头columns、数据源、数据源字段数组 -->
        <template v-for="table in (['UnionPay'].includes(currentClearOrg) ? reportTableInfo.UnionpayReportInfo : reportTableInfo.NuccAndAmexReportInfo )">
          <div :key="table.tableKey">
            <h3>{{ table.title }}</h3>
            <a-table
              :row-key="'id'"
              :columns="table.columns"
              :data-source="tableData[table.tableKey]"
              bordered
              :pagination="false"
              size="small"
              :scroll="{ x: table.fields.length * 180 }"
            >
              <template v-for="col in table.fields" :slot="col" slot-scope="text">
                <div :key="col" class="cell-base" :class="errCells.includes(table.tableKey + '_' + col) ? 'check-red' : ''">
                  <!-- pop -->
                  <a-popover :visible="recordPopVisibles[table.tableKey + '_' + col]" placement="rightBottom" :get-popup-container="triggerNode => triggerNode.parentElement" trigger="click">
                    <template slot="title">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-size: 1.1rem;">详情</span>
                        <span style="cursor: pointer;" type="link" @click="() => closePop(table.tableKey, col)"><a-icon type="close"/></span>
                      </div>
                    </template>
                    <template slot="content">
                      <div style="font-size: 0.9rem; margin: 12px 0 8px 0;"><b>计算过程</b></div>
                      <a-table
                        :columns="CalcProcessColumns"
                        :data-source="calcProcess"
                        bordered
                        :loading="loadingCalcProcess"
                        :row-key="'__id'"
                        :pagination="false"
                        size="small"
                        style="margin-left: 8px;"
                        :scroll="{ x: 'max-content' }"
                      >
                        <template slot="payAmount" slot-scope="_text">
                          <span>{{ (_text === 0 || _text) ? toThousands(_text) : '--' }}</span>
                        </template>
                      </a-table>
                      <div style="font-size: 0.9rem; margin: 12px 0 8px 0;"><b>调账记录</b></div>
                      <a-table
                        :columns="AdjustRecordColumns"
                        :data-source="adjustRecords"
                        bordered
                        :loading="loadingAdjustRecords"
                        :row-key="'__id'"
                        :pagination="false"
                        size="small"
                        style="margin-left: 8px;"
                        :scroll="{ x: 'max-content' }"
                      >
                        <template slot="amount" slot-scope="_text">
                          <span>{{ (_text === 0 || _text) ? toThousands(_text) : '--' }}</span>
                        </template>
                        <template slot="calculated" slot-scope="_text,_record">
                          <span style="display: flex; align-items: center;justify-content: flex-start;">
                            <!-- <span style="width: 4px;height: 4px;border-radius: 50%;background-color: red;margin-right: 8px;"></span>
                            <span>{{ _text === 0 ? '已计算' : '未计算' }}</span> -->
                            <a-tag :key="_record" :color="_text === 0 ? 'orange' : 'green'">{{ _text === 0 ? '未计算' : '已计算' }}</a-tag>
                          </span>
                        </template>
                      </a-table>
                      <a-button v-show="!['REPORT_PROCESS','SUCCESS',''].includes(currentStatus)" size="small" type="primary" style="width: 100%;margin-top: 8px;" @click="() => openAdjustModal(table.tableKey, col)">调账</a-button>
                    </template>
                    <span class="icon-record" @click="() => handleAdjustRecord(table.tableKey, col)">
                      <a-icon type="plus" style="font-size: 8px;"/>
                    </span>
                  </a-popover>
                  <span>{{ (text === 0 || text) ? toThousands(text) : '--' }}</span>
                </div>
              </template>
            </a-table>
          </div>
        </template>
      </div>
    </a-spin>

    <!-- 调账抽屉 -->
    <a-drawer :visible="adjustDrawerVisible" title="调账" width="50%" @close="closeAdjustModal">
      <a-spin :spinning="isSubmiting">
        <a-form-model ref="adjustForm" :model="adjustForm" :rules="adjustDrawerRules" :labelCol="adjustLayout.labelCol" :wrapperCol="adjustLayout.wrapperCol">
          <a-form-model-item label="清算机构">
            {{ getLabelFromEnum(currentClearOrg, organEnum) }}
          </a-form-model-item>
          <a-form-model-item label="报备日期">
            {{ currentDate }}
          </a-form-model-item>
          <a-form-model-item label="调整字段">
            {{ getLabelFromEnum(adjustForm.reportTable, reportFileTypeEnum) + '.' + adjustForm.reportTableField }}
          </a-form-model-item>
          <a-form-model-item label="业务类型">
            {{ adjustForm.bizType }}
          </a-form-model-item>
          <a-form-model-item required label="运算符号" prop="symbol">
            <!-- <a-input v-model="adjustForm.symbol" /> -->
            <a-radio-group
              v-model="adjustForm.symbol"
              :options="[
                { value: '+', label: '+' },
                { value: '-', label: '-' }
              ]">
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item required label="调账金额(元)" prop="amount">
            <a-input-number
              v-model="adjustForm.amount"
              style="width: 100%;"
              :precision="2"
              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',').replaceAll('-', '')"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              placeholder="请输入调账金额"
            />
          </a-form-model-item>
          <a-form-model-item required label="备注" prop="remark">
            <a-textarea v-model="adjustForm.remark" :rows="2" placeholder="请输入调账备注" />
          </a-form-model-item>
          <div
            :style="{
              position: 'absolute',
              right: 0,
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e9e9e9',
              padding: '10px 16px',
              background: '#fff',
              textAlign: 'right',
              zIndex: 1,
            }"
          >
            <a-button :style="{ marginRight: '8px' }" @click="closeAdjustModal">
              取消
            </a-button>
            <a-button :disabled="['REPORT_PROCESS','SUCCESS',''].includes(currentStatus)" type="primary" @click="submitAdjust">
              确认
            </a-button>
          </div>
        </a-form-model>
      </a-spin>
    </a-drawer>
  </div>
</template>

<style lang="less" scoped>
.summary-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  .amount {
    font-size: 20px;
    color: #333;
  }
  .err-expression {
    font-size: 20px;
    color: #333;
  }
}

.report-containe {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.err-tag {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.cell-base {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  justify-content: flex-start;
}
.icon-record {
  display: flex;
  width: 16px;
  height: 16px;
  border: 1px solid #e8e8e8;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: white;
}

::v-deep .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: transparent;
}
// ::v-deep .check-red {
//   background-color: red !important;
// }
::v-deep td:has(> .check-red:first-child) {
  background-color: #ff5f57 !important;
}

::v-deep .ant-form-item{
  margin-bottom: 8px;
}

::v-deep .ant-spin-container,.ant-spin-nested-loading {
  position: initial !important;
}

::v-deep .ant-empty-normal {
  margin: 0;
}
::v-deep .ant-table-body {
  overflow-x: auto !important;
}
</style>
