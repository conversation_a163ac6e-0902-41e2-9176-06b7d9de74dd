<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
import { billCheckBatch } from '_/api/payBillManage'

export default defineComponent({
  data() {
    return {
      pageCode: 'payBillManage',
      schema: {} as any,
      confirmBillVisible: false, // 确认账单付款
      preExtVisible: false, // 存在未核销预付款提示
      preExt: null, // 存在未核销完成的预付款==0 不存在(详情弹窗)。===1:存在（提示弹窗）
      batchData: {
        range: '-',
        prepayAmount: '-',
        verifiedAmount: '-',
        unVerifyAmount: '-',
        bankName: '-',
        settleAmount: '-',
        payedAmount: '-',
      }, // 弹窗内容
      preExtTitleData: {}, // 标题依赖的数据
      tableColums: [
        {
          dataIndex: 'orderNo',
          title: '账单编号',
        },
        {
          dataIndex: 'businessType,cardType',
          title: '业务类型\n卡类型',
          scopedSlots: { customRender: 'businessTypeCard' }
        },
        {
          dataIndex: 'oracleCode,specialBusinessDesc',
          title: 'Oracle编码\n特殊业务',
          scopedSlots: { customRender: 'oracleSpecial' }
        },
        {
          dataIndex: 'regulateServiceAmount,serviceAmount',
          title: '调账后手续费\n已付款手续费',
          scopedSlots: { customRender: 'serviceAmounts' }
        },
        {
          dataIndex: 'paymentStatus,billType',
          title: '付款状态\n账单类型',
          scopedSlots: { customRender: 'paymentBillType' }
        },
        {
          dataIndex: 'currentPayAmount',
          title: '本期付款金额',
          scopedSlots: { customRender: 'currentPayAmount' }
        },
      ],
      // 多行插槽名称数组 - 统一管理所有需要多行显示的插槽
      multiLineSlots: ['businessTypeCard', 'oracleSpecial', 'serviceAmounts', 'paymentBillType'],
      selectRowData: [] as any[], // 列表选中的一项或多项
      tableData: [] as any[],
      selectedRowKeys: [] as Array<number>, // 选中行的key数组
      selectedRows: [] as Array<object>, // 选中行的key数组
      BUSINESS_TYPE_ENUM: [] as Array<{label: string, value: string}>,
      CARD_TYPE_ENUM: [] as Array<{label: string, value: string}>,
      BILL_TYPE_ENUM: [
        {
          label: '结算账单',
          value: 'SETTLE',
        },
        {
          label: '预付账单',
          value: 'PREPAY',
        }
      ],
      PAYMENT_ENUM: [
        {
          label: '未付款',
          value: 'UN_PAY'
        },
        {
          label: '付款中',
          value: 'PAYING'
        },
        {
          label: '部分付款',
          value: 'PARTIAL_PAYED'
        },
        {
          label: '已付款',
          value: 'PAYED'
        },
      ]
    }
  },
  computed: {
    modalTitle() {
      const data = this.preExtTitleData as any
      return `${data.selectBankName || ''} - ${data.selectInstitutionType || ''} 存在未核销预付款，请检查！`
    },
  },
  methods: {
    // 获取所有枚举集合
    getDicts(data: any) {
      const res = data && JSON.parse(data)
      this.BUSINESS_TYPE_ENUM = res['BUSINESS_TYPE'].map((item: any) => {
        item.label = item.name
        item.value = item.code
        return item
      }) // 业务类型
      this.CARD_TYPE_ENUM = res['CARD_TYPE'].map((item: any) => {
        item.label = item.name
        item.value = item.code
        return item
      }) // 卡类型
      // this.getAllEnums('') // 账单类型
      console.log('getDicts', data)
    },
    forMatAmount(value: any) {
      return (value === 0 || value === '0' || value) ? value : '-'
    },
    getEnumValue(enums: any, v: any) {
      return (enums.find((item: any) => item.value === v) || {}).label || '-'
    },
    openModal() {
      // const flag = rows.every((row: any) => ['CHECKED'].includes(row.accountingCheckStatus) && ['PARTIAL_PAYED', 'UN_PAY'].includes(row.paymentStatus) && row.billType === 'SETTLE') || false
      if (this.preExt === '0') {
      // 不存在
        this.showDrawer()
      } else {
        // 存在未核销付款单
        this.showConfirm()
        // console.log('this.schema', this.schema.components.modal_15)
        // this.schema.components.modal_15.exposedVariables.visible = true
      }
    },
    // 付款单预校验
    isOpenConfirmBill(rowData: any, preExtTitleData: any) {
      this.preExtTitleData = preExtTitleData && JSON.parse(preExtTitleData) || {}
      this.tableData = []
      this.preExt = null
      this.selectRowData = rowData && JSON.parse(rowData) || []
      const rows = this.selectRowData
      const orders = [] as any
      rows.map((item: any) => {
        orders.push(item.orderNo)
        return item
      })

      billCheckBatch({
        prepayOrderNos: orders.toString()
        // prepayOrderNos: '*****************'
      }).then((res: any) => {
        const data = res.data
        this.preExt = data.preExt

        this.batchData = {
          ...data,
          range: data.startTime + ' ~ ' + data.endTime,
        }

        this.tableData = data.configCostPaymentBillEntities.map((i: any) => {
          // ['CHECKED'].includes(row.accountingCheckStatus) && ['PARTIAL_PAYED','UN_PAY'].includes(row.paymentStatus)
          const canCheck = ['CHECKED'].includes(i.accountingCheckStatus) && ['PARTIAL_PAYED', 'UN_PAY'].includes(i.paymentStatus)

          return {
            ...i,
            blank: '\u200B',
            canCheck,
            businessType: this.getEnumValue(this.BUSINESS_TYPE_ENUM, i.businessType),
            cardType: this.getEnumValue(this.CARD_TYPE_ENUM, i.cardType),
            paymentStatus: this.getEnumValue(this.PAYMENT_ENUM, i.paymentStatus),
            billType: this.getEnumValue(this.BILL_TYPE_ENUM, i.billType),
            specialBusinessDesc: i.specialBusiness ? '是' : '否',
            currentPayAmount: 0 // 初始化本期付款金额
          }
        }) || []
        this.openModal()
      }).catch(() => { })
    },
    afterVisibleChange(val: boolean) {
      console.log('visible', val)
    },
    onSelectChange(selectedRowKeys: Array<number>, selectedRows: Array<object>) {
      console.log('selectedRowKeys changed: ', selectedRowKeys)
      console.log('selectedRows changed: ', selectedRows)
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    // 处理本期付款金额输入
    handlePayAmountChange(value: number, record: any, index: number) {
      const newData = [...this.tableData] as any[]
      if (newData[index]) {
        newData[index].currentPayAmount = value || 0
        this.tableData = newData
      }
      // 同时更新选中行数据
      const selectedIndex = this.selectedRows.findIndex((row: any) => row === record)
      if (selectedIndex !== -1) {
        (this.selectedRows[selectedIndex] as any).currentPayAmount = value || 0
      }
    },
    // 计算选中账单的总付款金额
    getSelectedTotalAmount() {
      return this.selectedRows.reduce((total: number, row: any) => {
        return total + (row.currentPayAmount || 0)
      }, 0)
    },
    // 弹窗确认按钮
    handleOk() {
      this.handleCancel()
      this.showDrawer()
    },
    // 列表弹窗
    showDrawer() {
      this.confirmBillVisible = true
    },
    onClose() {
      this.confirmBillVisible = false
    },
    // 未核销弹窗
    showConfirm() {
      this.preExtVisible = true
    },
    handleCancel() {
      this.preExtVisible = false
    },
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
    // this.isOpenConfirmBill(null, null)
    // this.showDrawer()
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :schema="schema" @openModal="isOpenConfirmBill" @getDicts="getDicts"/>
    <a-modal
      width="560px"
      :title="modalTitle"
      :visible="preExtVisible"
      ok-text="无需核销"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-descriptions title=" " :column="2">
        <a-descriptions-item label="预付周期">
          {{ batchData.range || '-' }}
        </a-descriptions-item>
        <br/>
        <a-descriptions-item label="预付金额">
          {{ forMatAmount(batchData.prepayAmount) }}
        </a-descriptions-item>
        <br/>
        <a-descriptions-item label="已核销金额">
          {{ forMatAmount(batchData.verifiedAmount) }}
        </a-descriptions-item>
        <a-descriptions-item label="未核销金额">
          {{ forMatAmount(batchData.unVerifyAmount) }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="备注信息">
          {{ batchData.remark }}
        </a-descriptions-item> -->
      </a-descriptions>
    </a-modal>
    <a-drawer
      title="请确认账单是否可以提交付款？"
      placement="right"
      :visible="confirmBillVisible"
      :after-visible-change="afterVisibleChange"
      width="900"
      @close="onClose"
    >
      <div class="drawer-content">
        <a-descriptions title=" " :column="3">
          <a-descriptions-item label="收款方名称">
            {{ forMatAmount(batchData.bankName) }}
          </a-descriptions-item>
          <br/>
          <br/>
          <a-descriptions-item label="应付款金额">
            {{ forMatAmount(batchData.settleAmount) }}
          </a-descriptions-item>
          <a-descriptions-item label="已付款金额">
            {{ forMatAmount(batchData.payedAmount) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
      <br/>
      <a-table
        class="paybill-table"
        :columns="tableColums"
        :dataSource="tableData"
        :scroll="{ x: '100%' }"
        :pagination="false"
        bordered
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange
        }"
      >
        <template
          v-for="slotName in multiLineSlots"
          :slot="slotName"
          slot-scope="text, record, index, column"
        >
          <div v-for="field in column.dataIndex.split(',')" :key="`${slotName}-${field}`">
            {{ record[field] || '-' }}
          </div>
        </template>

        <template slot="currentPayAmount" slot-scope="text, record, index">
          <a-input-number
            :value="record.currentPayAmount"
            :min="0"
            :precision="2"
            placeholder="请输入金额"
            style="width: 100%"
            @change="(value) => handlePayAmountChange(value, record, index)"
          />
        </template>
      </a-table>
      选中账单应付金额: {{ getSelectedTotalAmount() }}
    </a-drawer>
  </div>
</template>
<style lang="less" scoped>
.paybill-table{
  ::v-deep .ant-table-thead > tr > th,
  ::v-deep .ant-table-tbody > tr > td{
    padding: 8px 8px;
    // background: transparent;
  }

  // 表头换行样式
  ::v-deep .ant-table-thead > tr > th {
    white-space: pre-line;
    // text-align: center;
    line-height: 1.4;
  }

  // 表格内容换行样式
  ::v-deep .ant-table-tbody > tr > td {
    div > div {
      line-height: 1.4;
      margin-bottom: 2px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
